<template>
  <div v-if="images.length > 0" class="festival-image-slider">
    <!-- 메인 이미지 표시 영역 -->
    <div class="relative bg-gray-100 rounded-lg overflow-hidden">
      <div class="aspect-video relative">
        <img
          :src="currentImage.file_url"
          :alt="currentImage.alt_text || '축제 이미지'"
          class="w-full h-full object-cover"
          @error="handleImageError"
        />
        
        <!-- 이미지 설명 오버레이 -->
        <div v-if="currentImage.description" class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
          <p class="text-white text-sm">{{ currentImage.description }}</p>
        </div>

        <!-- 이전/다음 버튼 (이미지가 2개 이상일 때만) -->
        <template v-if="images.length > 1">
          <button
            @click="previousImage"
            class="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 rounded-full p-2 transition-all duration-200 shadow-lg"
            :disabled="currentIndex === 0"
            :class="{ 'opacity-50 cursor-not-allowed': currentIndex === 0 }"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          
          <button
            @click="nextImage"
            class="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 rounded-full p-2 transition-all duration-200 shadow-lg"
            :disabled="currentIndex === images.length - 1"
            :class="{ 'opacity-50 cursor-not-allowed': currentIndex === images.length - 1 }"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </template>

        <!-- 이미지 카운터 -->
        <div v-if="images.length > 1" class="absolute top-3 right-3 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
          {{ currentIndex + 1 }} / {{ images.length }}
        </div>
      </div>
    </div>

    <!-- 썸네일 네비게이션 (이미지가 2개 이상일 때만) -->
    <div v-if="images.length > 1" class="mt-4">
      <div class="flex gap-2 overflow-x-auto pb-2">
        <button
          v-for="(image, index) in images"
          :key="image.id"
          @click="setCurrentImage(index)"
          class="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200"
          :class="{
            'border-blue-500 ring-2 ring-blue-200': index === currentIndex,
            'border-gray-200 hover:border-gray-300': index !== currentIndex
          }"
        >
          <img
            :src="image.file_url"
            :alt="image.alt_text || `썸네일 ${index + 1}`"
            class="w-full h-full object-cover"
            @error="handleImageError"
          />
        </button>
      </div>
    </div>

    <!-- 이미지 정보 -->
    <div class="mt-4 text-sm text-gray-600">
      <p>총 {{ images.length }}개의 이미지</p>
    </div>
  </div>

  <!-- 이미지가 없는 경우 -->
  <div v-else class="text-center py-8 text-gray-500">
    <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
    </svg>
    <p>등록된 이미지가 없습니다.</p>
  </div>
</template>

<script setup lang="ts">
interface FestivalImage {
  id: number;
  file_url: string;
  alt_text: string;
  description: string;
  width?: number;
  height?: number;
  is_thumbnail: boolean;
  display_order: number;
}

interface Props {
  festivalId: number;
}

const props = defineProps<Props>();

// 이미지 데이터 상태
const images = ref<FestivalImage[]>([]);
const currentIndex = ref(0);
const loading = ref(true);
const error = ref<string | null>(null);

// 현재 이미지
const currentImage = computed(() => images.value[currentIndex.value]);

// 이미지 데이터 로드
async function loadImages() {
  try {
    loading.value = true;
    error.value = null;

    const response = await $fetch<{
      success: boolean;
      data: {
        festival_id: number;
        images: FestivalImage[];
        total_count: number;
      };
    }>(`/api/public/festivals/${props.festivalId}/images`);

    if (response.success && response.data.images) {
      images.value = response.data.images;
      currentIndex.value = 0;
    } else {
      images.value = [];
    }
  } catch (err: any) {
    console.error('이미지 로드 실패:', err);
    error.value = '이미지를 불러오는 중 오류가 발생했습니다.';
    images.value = [];
  } finally {
    loading.value = false;
  }
}

// 이미지 네비게이션
function nextImage() {
  if (currentIndex.value < images.value.length - 1) {
    currentIndex.value++;
  }
}

function previousImage() {
  if (currentIndex.value > 0) {
    currentIndex.value--;
  }
}

function setCurrentImage(index: number) {
  if (index >= 0 && index < images.value.length) {
    currentIndex.value = index;
  }
}

// 이미지 로드 오류 처리
function handleImageError(e: Event) {
  if (import.meta.client) {
    try {
      const target = e.target as HTMLImageElement;
      // 1x1 투명 PNG 데이터 URL 사용
      target.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
      target.onerror = null;
    } catch (error) {
      console.warn('Image error handling failed:', error);
    }
  }
}

// 키보드 네비게이션
function handleKeydown(e: KeyboardEvent) {
  if (images.value.length <= 1) return;
  
  switch (e.key) {
    case 'ArrowLeft':
      e.preventDefault();
      previousImage();
      break;
    case 'ArrowRight':
      e.preventDefault();
      nextImage();
      break;
  }
}

// 컴포넌트 마운트 시 이미지 로드
onMounted(() => {
  loadImages();
  
  // 키보드 이벤트 리스너 등록
  if (import.meta.client) {
    document.addEventListener('keydown', handleKeydown);
  }
});

// 컴포넌트 언마운트 시 이벤트 리스너 제거
onUnmounted(() => {
  if (import.meta.client) {
    document.removeEventListener('keydown', handleKeydown);
  }
});

// festivalId가 변경되면 이미지 다시 로드
watch(() => props.festivalId, () => {
  loadImages();
});
</script>

<style scoped>
.festival-image-slider {
  @apply w-full;
}

/* 썸네일 스크롤바 스타일링 */
.festival-image-slider ::-webkit-scrollbar {
  height: 4px;
}

.festival-image-slider ::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

.festival-image-slider ::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded;
}

.festival-image-slider ::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}
</style>
